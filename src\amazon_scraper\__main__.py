"""
    Main module for amazon_scraper.
"""

import logging
import click
from typing import Optional
import concurrent.futures
import time

from amazon_scraper.collector import AmazonDataCollector
from amazon_scraper.config import AMAZON_CATEGORIES

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def scrape_category(category_info: tuple, max_pages: int = 5) -> None:
    category_name, category_url = category_info
    logger.info(f"Scraping category: {category_name}")
    collector = AmazonDataCollector(max_pages=max_pages)
    collector.collect_amazon_product_data(category_url)

def scrape_amazon_direct(url: Optional[str] = None, category: Optional[str] = None, scrape_all: bool = False, workers: int = 3, max_pages: int = 5) -> None:
    """
    Direct function to scrape Amazon without Click decorators.
    Can be called directly from Python code.
    """
    start_time = time.time()
    logger.info(f"Starting Amazon scraper - {max_pages} page(s) per category")

    if url:
        collector = AmazonDataCollector(max_pages=max_pages)
        collector.collect_amazon_product_data(url)
    elif category:
        if category not in AMAZON_CATEGORIES:
            available_categories = ", ".join(AMAZON_CATEGORIES.keys())
            logger.error(f"Category '{category}' not found. Available categories: {available_categories}")
            return
        collector = AmazonDataCollector(max_pages=max_pages)
        collector.collect_amazon_product_data(AMAZON_CATEGORIES[category])
    elif scrape_all:
        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:
            from functools import partial
            scrape_func = partial(scrape_category, max_pages=max_pages)
            executor.map(scrape_func, AMAZON_CATEGORIES.items())
    else:
        logger.error("Please provide either url, category, or scrape_all=True")

    end_time = time.time()
    total_time = end_time - start_time
    print(f"Total scraping time: {total_time:.2f} seconds")

@click.command()
@click.option(
    "--url",
    help="The url of the page for which to return Amazon product data for.",
    required=False,
)
@click.option(
    "--category",
    help="Category name to scrape from predefined categories.",
    required=False,
)
@click.option(
    "--all",
    "scrape_all",
    is_flag=True,
    help="Scrape all predefined categories.",
    required=False,
)
@click.option(
    "--workers",
    default=5,
    help="Number of parallel workers for scraping (default: 3)",
    required=False,
)
@click.option(
    "--max-pages",
    default=15,
    help="Maximum number of pages to scrape per category (default: 1)",
    required=False,
)
def scrape_amazon(url: Optional[str], category: Optional[str], scrape_all: bool, workers: int, max_pages: int) -> None:
    """Click command wrapper for CLI usage."""
    scrape_amazon_direct(url=url, category=category, scrape_all=scrape_all, workers=workers, max_pages=max_pages)

if __name__ == "__main__":
    scrape_amazon()